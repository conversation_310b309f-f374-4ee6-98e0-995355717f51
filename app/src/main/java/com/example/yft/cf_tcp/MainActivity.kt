package com.example.yft.cf_tcp

import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

class MainActivity : AppCompatActivity() {

    private lateinit var btnConnect: Button
    private lateinit var btnSendData: Button
    private lateinit var btnDisconnect: Button
    private lateinit var tvLog: TextView

    private var tcpClient: TcpClient? = null
    private var currentGpggaIndex = 0

    // GPGGA测试数据
    private val gpggaDataList = listOf(
        "\$GPGGA,073641.00,2807.01270688,N,11257.82945651,E,5,35,0.5,126.1033,M,-15.9610,M,03,1492*4D",
        "\$GPGGA,073642.00,2807.01270537,N,11257.82945292,E,5,35,0.5,126.1095,M,-15.9610,M,02,1492*4F",
        "\$GPGGA,073643.00,2807.01270789,N,11257.82945376,E,5,35,0.5,126.1043,M,-15.9610,M,02,1492*49",
        "\$GPGGA,073644.00,2807.01270833,N,11257.82944652,E,5,35,0.5,126.1265,M,-15.9610,M,03,1492*45"
    )

    companion object {
        private const val SERVER_HOST = "***********"
        private const val SERVER_PORT = 60004
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        initViews()
        setupClickListeners()
        logMessage("应用启动完成")
    }

    private fun initViews() {
        btnConnect = findViewById(R.id.btnConnect)
        btnSendData = findViewById(R.id.btnSendData)
        btnDisconnect = findViewById(R.id.btnDisconnect)
        tvLog = findViewById(R.id.tvLog)
    }

    private fun setupClickListeners() {
        btnConnect.setOnClickListener {
            connectToServer()
        }

        btnSendData.setOnClickListener {
            sendGpggaData()
        }

        btnDisconnect.setOnClickListener {
            disconnectFromServer()
        }
    }

    private fun connectToServer() {
        logMessage("正在连接服务器 $SERVER_HOST:$SERVER_PORT...")
        btnConnect.isEnabled = false

        lifecycleScope.launch {
            try {
                tcpClient = TcpClient(
                    serverHost = SERVER_HOST,
                    serverPort = SERVER_PORT,
                    onMessageReceived = { msg ->
                        handleReceivedMessage(msg)
                    },
                    onConnectionStatusChanged = { connected ->
                        handleConnectionStatusChanged(connected)
                    },
                    onError = { error ->
                        handleError(error)
                    }
                )

                val success = tcpClient?.connect() ?: false
                if (success) {
                    logMessage("连接成功!")
                } else {
                    logMessage("连接失败")
                    btnConnect.isEnabled = true
                }
            } catch (e: Exception) {
                logMessage("连接异常: ${e.message}")
                btnConnect.isEnabled = true
            }
        }
    }

    private fun sendGpggaData() {
        val gpggaData = gpggaDataList[currentGpggaIndex]
        currentGpggaIndex = (currentGpggaIndex + 1) % gpggaDataList.size

        logMessage("发送GPGGA数据: $gpggaData")

        lifecycleScope.launch {
            try {
                val success = tcpClient?.sendGpggaData(gpggaData) ?: false
                if (success) {
                    logMessage("数据发送成功")
                } else {
                    logMessage("数据发送失败")
                }
            } catch (e: Exception) {
                logMessage("发送异常: ${e.message}")
            }
        }
    }

    private fun disconnectFromServer() {
        logMessage("正在断开连接...")
        tcpClient?.disconnect()
        tcpClient = null

        btnConnect.isEnabled = true
        btnSendData.isEnabled = false
        btnDisconnect.isEnabled = false

        logMessage("已断开连接")
    }

    private fun handleReceivedMessage(msg: MessageProtobuf.Msg) {
        lifecycleScope.launch(Dispatchers.Main) {
            try {
                val headInfo = if (msg.hasHead()) {
                    val head = msg.head
                    buildString {
                        append("消息头信息: ")
                        if (head.msgId.isNotEmpty()) append("ID=${head.msgId} ")
                        if (head.msgType != 0) append("类型=${head.msgType} ")
                        if (head.fromId.isNotEmpty()) append("发送者=${head.fromId} ")
                        if (head.toId.isNotEmpty()) append("接收者=${head.toId} ")
                        if (head.timestamp != 0L) append("时间=${head.timestamp} ")
                    }
                } else {
                    "无消息头信息"
                }

                logMessage("收到服务器响应:")
                if (headInfo != "无消息头信息") {
                    logMessage("  $headInfo")
                }

                val bodyContent = msg.body.trim()
                if (bodyContent.isNotEmpty()) {
                    // 显示十六进制数据的长度和前面部分
                    val hexLength = bodyContent.length
                    val displayHex = if (hexLength > 100) {
                        "${bodyContent.take(100)}... (总长度: $hexLength 字符)"
                    } else {
                        bodyContent
                    }

                    logMessage("  十六进制数据: $displayHex")
                    logMessage("  数据长度: ${hexLength / 2} 字节")

                    // 尝试解析一些可能的文本内容
                    if (bodyContent.contains("53494E4F474E5353")) { // "SINOGNSS" 的十六进制
                        logMessage("  -> 检测到SINOGNSS设备响应")
                    }

                } else {
                    logMessage("  消息内容为空")
                }

            } catch (e: Exception) {
                logMessage("处理接收消息时出错: ${e.message}")
            }
        }
    }

    private fun handleConnectionStatusChanged(connected: Boolean) {
        lifecycleScope.launch(Dispatchers.Main) {
            btnConnect.isEnabled = !connected
            btnSendData.isEnabled = connected
            btnDisconnect.isEnabled = connected

            if (connected) {
                logMessage("连接状态: 已连接")
            } else {
                logMessage("连接状态: 已断开")
            }
        }
    }

    private fun handleError(error: String) {
        lifecycleScope.launch(Dispatchers.Main) {
            logMessage("错误: $error")
        }
    }

    private fun logMessage(message: String) {
        val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
        val logEntry = "[$timestamp] $message\n"

        runOnUiThread {
            tvLog.append(logEntry)
            // 自动滚动到底部
            val scrollView = tvLog.parent as? android.widget.ScrollView
            scrollView?.post {
                scrollView.fullScroll(android.view.View.FOCUS_DOWN)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        tcpClient?.disconnect()
        tcpClient = null
    }
}