package com.example.yft.cf_tcp

import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

class MainActivity : AppCompatActivity() {

    private lateinit var btnConnect: Button
    private lateinit var btnSendData: Button
    private lateinit var btnDisconnect: Button
    private lateinit var tvLog: TextView

    private var tcpClient: TcpClient? = null
    private var currentGpggaIndex = 0

    // GPGGA测试数据
    private val gpggaDataList = listOf(
        "\$GPGGA,073641.00,2807.01270688,N,11257.82945651,E,5,35,0.5,126.1033,M,-15.9610,M,03,1492*4D",
        "\$GPGGA,073642.00,2807.01270537,N,11257.82945292,E,5,35,0.5,126.1095,M,-15.9610,M,02,1492*4F",
        "\$GPGGA,073643.00,2807.01270789,N,11257.82945376,E,5,35,0.5,126.1043,M,-15.9610,M,02,1492*49",
        "\$GPGGA,073644.00,2807.01270833,N,11257.82944652,E,5,35,0.5,126.1265,M,-15.9610,M,03,1492*45"
    )

    companion object {
        private const val SERVER_HOST = "***********"
        private const val SERVER_PORT = 60004
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        initViews()
        setupClickListeners()
        logMessage("应用启动完成")
    }

    private fun initViews() {
        btnConnect = findViewById(R.id.btnConnect)
        btnSendData = findViewById(R.id.btnSendData)
        btnDisconnect = findViewById(R.id.btnDisconnect)
        tvLog = findViewById(R.id.tvLog)
    }

    private fun setupClickListeners() {
        btnConnect.setOnClickListener {
            connectToServer()
        }

        btnSendData.setOnClickListener {
            sendGpggaData()
        }

        btnDisconnect.setOnClickListener {
            disconnectFromServer()
        }
    }