package com.example.yft.cf_tcp

import android.util.Log
import kotlinx.coroutines.*
import java.io.*
import java.net.Socket
import java.net.SocketTimeoutException
import java.nio.charset.Charset

class TcpClient(
    private val serverHost: String,
    private val serverPort: Int,
    private val onMessageReceived: (MessageProtobuf.Msg) -> Unit,
    private val onConnectionStatusChanged: (Boolean) -> Unit,
    private val onError: (String) -> Unit
) {
    private var socket: Socket? = null
    private var outputStream: OutputStream? = null
    private var inputStream: InputStream? = null
    private var isConnected = false
    private var receiveJob: Job? = null
    
    companion object {
        private const val TAG = "TcpClient"
        private const val CONNECT_TIMEOUT = 10000 // 10秒连接超时
        private const val READ_TIMEOUT = 30000 // 30秒读取超时
    }

    suspend fun connect(): Boolean = withContext(Dispatchers.IO) {
        try {
            if (isConnected) {
                Log.w(TAG, "Already connected")
                return@withContext true
            }

            Log.d(TAG, "Connecting to $serverHost:$serverPort")
            
            socket = Socket().apply {
                soTimeout = READ_TIMEOUT
                connect(java.net.InetSocketAddress(serverHost, serverPort), CONNECT_TIMEOUT)
            }
            
            outputStream = socket?.getOutputStream()
            inputStream = socket?.getInputStream()
            
            isConnected = true
            onConnectionStatusChanged(true)
            
            // 启动接收消息的协程
            startReceiving()
            
            Log.d(TAG, "Connected successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Connection failed", e)
            onError("连接失败: ${e.message}")
            cleanup()
            false
        }
    }

    private fun startReceiving() {
        receiveJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                val buffer = ByteArray(4096)
                val messageBuffer = mutableListOf<Byte>()

                while (isConnected && !isActive.not()) {
                    try {
                        val bytesRead = inputStream?.read(buffer) ?: -1
                        if (bytesRead > 0) {
                            val receivedData = buffer.copyOf(bytesRead)
                            Log.d(TAG, "Received ${bytesRead} bytes")

                            // 显示十六进制数据
                            val hexData = receivedData.joinToString(" ") { "%02x".format(it) }
                            Log.d(TAG, "Received data as hex: $hexData")

                            // 检查是否包含可见的ASCII文本
                            val asciiText = receivedData.map { byte ->
                                if (byte in 32..126) byte.toInt().toChar() else '.'
                            }.joinToString("")
                            Log.d(TAG, "Received data as ASCII: $asciiText")

                            // 尝试多种编码方式处理文本
                            val textDataUTF8 = String(receivedData, Charsets.UTF_8)
                            val textDataGBK = try {
                                String(receivedData, Charset.forName("GBK"))
                            } catch (e: Exception) {
                                textDataUTF8
                            }
                            val textDataISO = String(receivedData, Charsets.ISO_8859_1)

                            Log.d(TAG, "Received data as UTF-8: ${textDataUTF8.take(100)}...")
                            Log.d(TAG, "Received data as GBK: ${textDataGBK.take(100)}...")
                            Log.d(TAG, "Received data as ISO-8859-1: ${textDataISO.take(100)}...")

                            // 将数据添加到缓冲区
                            messageBuffer.addAll(receivedData.toList())

                            // 尝试解析完整的消息
                            processReceivedData(messageBuffer)

                        } else if (bytesRead == -1) {
                            Log.d(TAG, "Server closed connection")
                            break
                        }
                    } catch (e: SocketTimeoutException) {
                        // 读取超时，继续循环
                        continue
                    } catch (e: Exception) {
                        Log.e(TAG, "Error reading from socket", e)
                        break
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Receive loop error", e)
                onError("接收数据错误: ${e.message}")
            } finally {
                disconnect()
            }
        }
    }

    private fun processReceivedData(messageBuffer: MutableList<Byte>) {
        try {
            // 首先尝试解析为protobuf消息
            val dataArray = messageBuffer.toByteArray()

            // 尝试多种解析方式
            var parsed = false

            // 方式1: 直接解析整个数据为protobuf
            if (!parsed) {
                try {
                    val msg = MessageProtobuf.Msg.parseFrom(dataArray)
                    Log.d(TAG, "Successfully parsed protobuf message: ${msg.body}")
                    onMessageReceived(msg)
                    messageBuffer.clear()
                    parsed = true
                } catch (e: Exception) {
                    Log.d(TAG, "Failed to parse as complete protobuf: ${e.message}")
                }
            }

            // 方式2: 查找protobuf消息边界（如果有长度前缀）
            if (!parsed && messageBuffer.size >= 4) {
                try {
                    // 尝试读取长度前缀（4字节大端序）
                    val length = ((messageBuffer[0].toInt() and 0xFF) shl 24) or
                                ((messageBuffer[1].toInt() and 0xFF) shl 16) or
                                ((messageBuffer[2].toInt() and 0xFF) shl 8) or
                                (messageBuffer[3].toInt() and 0xFF)

                    if (length > 0 && length < 1024 * 1024 && messageBuffer.size >= 4 + length) {
                        val msgData = messageBuffer.subList(4, 4 + length).toByteArray()
                        val msg = MessageProtobuf.Msg.parseFrom(msgData)
                        Log.d(TAG, "Successfully parsed protobuf with length prefix: ${msg.body}")
                        onMessageReceived(msg)

                        // 移除已处理的数据
                        repeat(4 + length) { messageBuffer.removeAt(0) }
                        parsed = true
                    }
                } catch (e: Exception) {
                    Log.d(TAG, "Failed to parse with length prefix: ${e.message}")
                }
            }

            // 方式3: 如果都失败了，作为文本消息处理
            if (!parsed && messageBuffer.isNotEmpty()) {
                val bestTextData = detectBestEncoding(dataArray)
                if (bestTextData.isNotEmpty()) {
                    Log.d(TAG, "Processing as text message: $bestTextData")

                    // 创建一个简单的protobuf消息来包装文本数据
                    val msg = MessageProtobuf.Msg.newBuilder()
                        .setBody(bestTextData)
                        .build()

                    onMessageReceived(msg)
                    messageBuffer.clear()
                    parsed = true
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error processing received data", e)
            onError("处理接收数据失败: ${e.message}")
        }
    }

    private fun detectBestEncoding(data: ByteArray): String {
        // 首先检查是否是纯二进制数据
        val hexData = data.joinToString(" ") { "%02x".format(it) }
        Log.d(TAG, "Raw hex data: $hexData")

        // 检查ASCII可见字符比例
        val visibleAsciiCount = data.count { it in 32..126 }
        val asciiRatio = visibleAsciiCount.toFloat() / data.size

        Log.d(TAG, "ASCII visible chars: $visibleAsciiCount/${data.size} (${(asciiRatio * 100).toInt()}%)")

        // 如果ASCII可见字符比例太低，可能是二进制数据
        if (asciiRatio < 0.3) {
            Log.d(TAG, "Data appears to be binary, creating hex representation")
            return "Binary data (${data.size} bytes): ${hexData.take(200)}${if (hexData.length > 200) "..." else ""}"
        }

        val encodings = listOf(
            Charsets.UTF_8 to "UTF-8",
            Charset.forName("GBK") to "GBK",
            Charset.forName("GB2312") to "GB2312",
            Charsets.ISO_8859_1 to "ISO-8859-1",
            Charset.forName("Windows-1252") to "Windows-1252"
        )

        var bestText = ""
        var bestScore = -1
        var bestEncoding = "Unknown"

        for ((charset, name) in encodings) {
            try {
                val text = String(data, charset).trim()
                val score = calculateTextQuality(text)

                Log.d(TAG, "Encoding $name: '${text.take(50)}...' (score: $score)")

                if (score > bestScore) {
                    bestScore = score
                    bestText = text
                    bestEncoding = name
                }
            } catch (e: Exception) {
                Log.d(TAG, "Failed to decode with $name: ${e.message}")
            }
        }

        Log.d(TAG, "Best encoding: $bestEncoding with score: $bestScore")
        return if (bestScore > 0) bestText else "Binary/Unknown data: $hexData"
    }

    private fun calculateTextQuality(text: String): Int {
        if (text.isEmpty()) return -1

        var score = 0

        // 检查是否包含可打印字符
        val printableChars = text.count { it.isLetterOrDigit() || it.isWhitespace() || ".,;:!?-_()[]{}\"'".contains(it) }
        score += printableChars * 2

        // 惩罚控制字符和不可见字符
        val controlChars = text.count { it.isISOControl() && it != '\n' && it != '\r' && it != '\t' }
        score -= controlChars * 5

        // 奖励常见的响应模式
        if (text.contains("OK", ignoreCase = true)) score += 10
        if (text.contains("ACK", ignoreCase = true)) score += 10
        if (text.contains("GPGGA", ignoreCase = true)) score += 10
        if (text.contains("SUCCESS", ignoreCase = true)) score += 10
        if (text.contains("ERROR", ignoreCase = true)) score += 5

        // 惩罚过多的重复字符
        val uniqueChars = text.toSet().size
        if (uniqueChars < text.length / 4) score -= 10

        return score
    }

    suspend fun sendGpggaData(gpggaData: String): Boolean = withContext(Dispatchers.IO) {
        try {
            if (!isConnected || outputStream == null) {
                onError("未连接到服务器")
                return@withContext false
            }

            val dataToSend = gpggaData + "\r\n"
            val bytes = dataToSend.toByteArray(Charsets.UTF_8)
            
            Log.d(TAG, "Sending GPGGA data: $gpggaData")
            outputStream?.write(bytes)
            outputStream?.flush()
            
            Log.d(TAG, "GPGGA data sent successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send GPGGA data", e)
            onError("发送数据失败: ${e.message}")
            false
        }
    }

    fun disconnect() {
        Log.d(TAG, "Disconnecting...")
        
        receiveJob?.cancel()
        receiveJob = null
        
        cleanup()
        
        if (isConnected) {
            isConnected = false
            onConnectionStatusChanged(false)
        }
        
        Log.d(TAG, "Disconnected")
    }

    private fun cleanup() {
        try {
            outputStream?.close()
            inputStream?.close()
            socket?.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup", e)
        } finally {
            outputStream = null
            inputStream = null
            socket = null
        }
    }

    fun isConnected(): Boolean = isConnected
}
