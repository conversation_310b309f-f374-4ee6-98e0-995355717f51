package com.example.yft.cf_tcp

import android.util.Log
import kotlinx.coroutines.*
import java.io.*
import java.net.Socket
import java.net.SocketTimeoutException
import java.nio.charset.Charset

class TcpClient(
    private val serverHost: String,
    private val serverPort: Int,
    private val onMessageReceived: (MessageProtobuf.Msg) -> Unit,
    private val onConnectionStatusChanged: (Boolean) -> Unit,
    private val onError: (String) -> Unit
) {
    private var socket: Socket? = null
    private var outputStream: OutputStream? = null
    private var inputStream: InputStream? = null
    private var isConnected = false
    private var receiveJob: Job? = null
    
    companion object {
        private const val TAG = "TcpClient"
        private const val CONNECT_TIMEOUT = 10000 // 10秒连接超时
        private const val READ_TIMEOUT = 30000 // 30秒读取超时
    }

    suspend fun connect(): Boolean = withContext(Dispatchers.IO) {
        try {
            if (isConnected) {
                Log.w(TAG, "Already connected")
                return@withContext true
            }

            Log.d(TAG, "Connecting to $serverHost:$serverPort")
            
            socket = Socket().apply {
                soTimeout = READ_TIMEOUT
                connect(java.net.InetSocketAddress(serverHost, serverPort), CONNECT_TIMEOUT)
            }
            
            outputStream = socket?.getOutputStream()
            inputStream = socket?.getInputStream()
            
            isConnected = true
            onConnectionStatusChanged(true)
            
            // 启动接收消息的协程
            startReceiving()
            
            Log.d(TAG, "Connected successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Connection failed", e)
            onError("连接失败: ${e.message}")
            cleanup()
            false
        }
    }

    private fun startReceiving() {
        receiveJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                val buffer = ByteArray(4096)

                while (isConnected && !isActive.not()) {
                    try {
                        val bytesRead = inputStream?.read(buffer) ?: -1
                        if (bytesRead > 0) {
                            val receivedData = buffer.copyOf(bytesRead)
                            Log.d(TAG, "Received ${bytesRead} bytes")

                            // 将字节数组转换为十六进制字符串（参照Java代码的ByteUtil.bytes2HexStr方法）
                            val hexString = bytes2HexStr(receivedData)
                            Log.d(TAG, "Received data as hex: $hexString")

                            // 创建protobuf消息（参照Java代码的getReceiveMsg方法）
                            val receivedMsg = getReceiveMsg(hexString)

                            // 直接处理消息
                            onMessageReceived(receivedMsg)

                        } else if (bytesRead == -1) {
                            Log.d(TAG, "Server closed connection")
                            break
                        }
                    } catch (e: SocketTimeoutException) {
                        // 读取超时，继续循环
                        continue
                    } catch (e: Exception) {
                        Log.e(TAG, "Error reading from socket", e)
                        break
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Receive loop error", e)
                onError("接收数据错误: ${e.message}")
            } finally {
                disconnect()
            }
        }
    }



    /**
     * 将字节数组转换为十六进制字符串
     * 参照Java代码的ByteUtil.bytes2HexStr方法
     */
    private fun bytes2HexStr(bytes: ByteArray): String {
        val hexChars = "0123456789ABCDEF"
        val result = StringBuilder(bytes.size * 2)

        for (byte in bytes) {
            val value = byte.toInt() and 0xFF
            result.append(hexChars[value shr 4])
            result.append(hexChars[value and 0x0F])
        }

        return result.toString()
    }

    /**
     * 创建接收消息的protobuf对象
     * 参照Java代码的getReceiveMsg方法
     */
    private fun getReceiveMsg(content: String): MessageProtobuf.Msg {
        val headBuilder = MessageProtobuf.Head.newBuilder()
        val builder = MessageProtobuf.Msg.newBuilder()
            .setHead(headBuilder)
            .setBody(content)

        return builder.build()
    }



    suspend fun sendGpggaData(gpggaData: String): Boolean = withContext(Dispatchers.IO) {
        try {
            if (!isConnected || outputStream == null) {
                onError("未连接到服务器")
                return@withContext false
            }

            val dataToSend = gpggaData + "\r\n"
            val bytes = dataToSend.toByteArray(Charsets.UTF_8)
            
            Log.d(TAG, "Sending GPGGA data: $gpggaData")
            outputStream?.write(bytes)
            outputStream?.flush()
            
            Log.d(TAG, "GPGGA data sent successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send GPGGA data", e)
            onError("发送数据失败: ${e.message}")
            false
        }
    }

    fun disconnect() {
        Log.d(TAG, "Disconnecting...")
        
        receiveJob?.cancel()
        receiveJob = null
        
        cleanup()
        
        if (isConnected) {
            isConnected = false
            onConnectionStatusChanged(false)
        }
        
        Log.d(TAG, "Disconnected")
    }

    private fun cleanup() {
        try {
            outputStream?.close()
            inputStream?.close()
            socket?.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup", e)
        } finally {
            outputStream = null
            inputStream = null
            socket = null
        }
    }

    fun isConnected(): Boolean = isConnected
}
