### 需求
实现一个Android上的TCP连接,目标地址 218.77.59.2:60004
然后发送GPGGA数据给这个服务器
$GPGGA,073641.00,2807.01270688,N,11257.82945651,E,5,35,0.5,126.1033,M,-15.9610,M,03,1492*4D
$GPGGA,073642.00,2807.01270537,N,11257.82945292,E,5,35,0.5,126.1095,M,-15.9610,M,02,1492*4F
$GPGGA,073643.00,2807.01270789,N,11257.82945376,E,5,35,0.5,126.1043,M,-15.9610,M,02,1492*49
$GPGGA,073644.00,2807.01270833,N,11257.82944652,E,5,35,0.5,126.1265,M,-15.9610,M,03,1492*45
使用字符串的形式发送，要在结尾加上`\r\n` 这个字符串

同时要求处理接受服务器返回的结果数据，类型是MessageProtobuf.Msg